[{"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openai\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openrouter\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\emotional-tone\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\vibe\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AiAssistant.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ApiSettings.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ApiSettingsButton.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AutoSaveIndicator.tsx": "15", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\Header.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\LanguageToggle.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ModuleLayout.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\OutputPanel.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ProgressIndicator.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartQuestion.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeProvider.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeToggle.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\store\\contextStore.ts": "24"}, {"size": 1145, "mtime": 1751756292092, "results": "25", "hashOfConfig": "26"}, {"size": 1281, "mtime": 1751756314810, "results": "27", "hashOfConfig": "26"}, {"size": 4711, "mtime": 1751752039368, "results": "28", "hashOfConfig": "26"}, {"size": 5406, "mtime": 1751752064185, "results": "29", "hashOfConfig": "26"}, {"size": 11119, "mtime": 1751751211373, "results": "30", "hashOfConfig": "26"}, {"size": 755, "mtime": 1751756172514, "results": "31", "hashOfConfig": "26"}, {"size": 7233, "mtime": 1751752111799, "results": "32", "hashOfConfig": "26"}, {"size": 10485, "mtime": 1751756134293, "results": "33", "hashOfConfig": "26"}, {"size": 4641, "mtime": 1751752012669, "results": "34", "hashOfConfig": "26"}, {"size": 6963, "mtime": 1751752090185, "results": "35", "hashOfConfig": "26"}, {"size": 2582, "mtime": 1751749235443, "results": "36", "hashOfConfig": "26"}, {"size": 6525, "mtime": 1751756272373, "results": "37", "hashOfConfig": "26"}, {"size": 7159, "mtime": 1751755830747, "results": "38", "hashOfConfig": "26"}, {"size": 1476, "mtime": 1751755857195, "results": "39", "hashOfConfig": "26"}, {"size": 1584, "mtime": 1751752141042, "results": "40", "hashOfConfig": "26"}, {"size": 1639, "mtime": 1751756057080, "results": "41", "hashOfConfig": "26"}, {"size": 1882, "mtime": 1751755786921, "results": "42", "hashOfConfig": "26"}, {"size": 4031, "mtime": 1751752183700, "results": "43", "hashOfConfig": "26"}, {"size": 7577, "mtime": 1751759304140, "results": "44", "hashOfConfig": "26"}, {"size": 5420, "mtime": 1751751875523, "results": "45", "hashOfConfig": "26"}, {"size": 5008, "mtime": 1751751829314, "results": "46", "hashOfConfig": "26"}, {"size": 1709, "mtime": 1751749793754, "results": "47", "hashOfConfig": "26"}, {"size": 2382, "mtime": 1751755718062, "results": "48", "hashOfConfig": "26"}, {"size": 5088, "mtime": 1751756001911, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bng5zs", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openai\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openrouter\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\emotional-tone\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\vibe\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AiAssistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ApiSettings.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ApiSettingsButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AutoSaveIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\LanguageToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ModuleLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\OutputPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ProgressIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartQuestion.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\store\\contextStore.ts", [], []]