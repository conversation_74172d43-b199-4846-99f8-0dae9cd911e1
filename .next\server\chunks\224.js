exports.id=224,exports.ids=[224],exports.modules={2829:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},765:(e,t,r)=>{Promise.resolve().then(r.bind(r,4609))},7515:(e,t,r)=>{"use strict";r.d(t,{default:()=>v});var a=r(326),o=r(434),s=r(4609);function n(){let{theme:e,toggleTheme:t}=(0,s.F)();return(0,a.jsxs)("button",{onClick:t,className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":`Switch to ${"light"===e?"dark":"light"} mode`,title:`Switch to ${"light"===e?"dark":"light"} mode`,children:[(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[a.jsx("svg",{className:`w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${"light"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}),a.jsx("svg",{className:`w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${"dark"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})]}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}var i=r(9190);function l(){let{currentLanguage:e,setLanguage:t}=(0,i.l)();return(0,a.jsxs)("button",{onClick:()=>t("ar"===e?"en":"ar"),className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":"ar"===e?"Switch to English":"التبديل إلى العربية",title:"ar"===e?"Switch to English":"التبديل إلى العربية",children:[(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[a.jsx("span",{className:`text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${"en"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"}`,children:"EN"}),a.jsx("span",{className:`text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${"ar"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"}`,children:"عر"})]}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}var c=r(7577),d=r(8378),g=r(4019),x=r(6828),u=r(1216),p=r(2714),h=r(4014),m=r(1215);function b({isOpen:e,onClose:t}){let{apiSettings:r,setApiSettings:o,currentLanguage:s}=(0,i.l)(),[n,l]=(0,c.useState)(r),[b,y]=(0,c.useState)(!1),[v,f]=(0,c.useState)(!1),k="ar"===s,j={title:k?"إعدادات API":"API Settings",openaiLabel:k?"مفتاح OpenAI API":"OpenAI API Key",openrouterLabel:k?"مفتاح OpenRouter API":"OpenRouter API Key",openaiPlaceholder:k?"أدخل مفتاح OpenAI API الخاص بك":"Enter your OpenAI API key",openrouterPlaceholder:k?"أدخل مفتاح OpenRouter API الخاص بك":"Enter your OpenRouter API key",save:k?"حفظ":"Save",cancel:k?"إلغاء":"Cancel",description:k?"قم بإعداد مفاتيح API الخاصة بك لتمكين الإجابات التفاعلية المدعومة بالذكاء الاصطناعي":"Configure your API keys to enable AI-powered interactive responses",openaiDesc:k?"للوصول إلى نماذج GPT من OpenAI":"For accessing GPT models from OpenAI",openrouterDesc:k?"للوصول إلى نماذج متعددة من مقدمي خدمات مختلفين":"For accessing multiple models from various providers"};return e?a.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md border border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:a.jsx(d.Z,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),a.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white font-arabic",children:j.title})]}),a.jsx("button",{onClick:t,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:a.jsx(g.Z,{className:"w-5 h-5 text-gray-500"})})]}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 font-arabic",children:j.description}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic",children:[a.jsx(x.Z,{className:"w-4 h-4"}),j.openaiLabel]}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 font-arabic",children:j.openaiDesc}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("input",{type:v?"text":"password",value:n.openaiApiKey,onChange:e=>l(t=>({...t,openaiApiKey:e.target.value})),placeholder:j.openaiPlaceholder,className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all font-arabic",dir:k?"rtl":"ltr"}),a.jsx("button",{type:"button",onClick:()=>f(!v),className:"absolute right-3 top-1/2 -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:v?a.jsx(u.Z,{className:"w-4 h-4"}):a.jsx(p.Z,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic",children:[a.jsx(h.Z,{className:"w-4 h-4"}),j.openrouterLabel]}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 font-arabic",children:j.openrouterDesc}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("input",{type:b?"text":"password",value:n.openrouterApiKey,onChange:e=>l(t=>({...t,openrouterApiKey:e.target.value})),placeholder:j.openrouterPlaceholder,className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all font-arabic",dir:k?"rtl":"ltr"}),a.jsx("button",{type:"button",onClick:()=>y(!b),className:"absolute right-3 top-1/2 -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:b?a.jsx(u.Z,{className:"w-4 h-4"}):a.jsx(p.Z,{className:"w-4 h-4"})})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700",children:[a.jsx("button",{onClick:()=>{l(r),t()},className:"flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors font-arabic",children:j.cancel}),(0,a.jsxs)("button",{onClick:()=>{o(n),t()},className:"flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-arabic",children:[a.jsx(m.Z,{className:"w-4 h-4"}),j.save]})]})]})}):null}function y(){let[e,t]=(0,c.useState)(!1),{currentLanguage:r}=(0,i.l)(),o="ar"===r?"إعدادات API":"API Settings";return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:()=>t(!0),className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":o,title:o,children:[a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx(d.Z,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90"})}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),a.jsx(b,{isOpen:e,onClose:()=>t(!1)})]})}function v({title:e,subtitle:t,backLink:r,emoji:s}){return(0,a.jsxs)("header",{className:"relative",children:[a.jsx("div",{className:"fixed top-4 right-4 z-50 flex gap-3",children:a.jsx("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[a.jsx(y,{}),a.jsx(l,{}),a.jsx(n,{})]})})}),(0,a.jsxs)("div",{className:"text-center mb-8 pt-4",children:[r&&a.jsx(o.default,{href:r.href,className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors",children:r.label}),(0,a.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2",children:[s&&a.jsx("span",{className:"mr-2",children:s}),e]}),t&&a.jsx("p",{className:"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:t})]})]})}},4609:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,ThemeProvider:()=>n});var a=r(326),o=r(7577);let s=(0,o.createContext)(void 0);function n({children:e}){let[t,r]=(0,o.useState)("light"),[n,i]=(0,o.useState)(!1);return n?a.jsx(s.Provider,{value:{theme:t,toggleTheme:()=>{r(e=>"light"===e?"dark":"light")}},children:e}):a.jsx(a.Fragment,{children:e})}function i(){let e=(0,o.useContext)(s);return void 0===e?{theme:"light",toggleTheme:()=>{}}:e}},9190:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});var a=r(8408),o=r(5251);let s={projectDefinition:{name:"",purpose:"",targetUsers:"",goals:"",scope:"",timeline:""},contextMap:{timeContext:"",language:"",location:"",culturalContext:"",behavioralAspects:"",environmentalFactors:""},emotionalTone:{personality:"",communicationStyle:"",userExperience:"",brandVoice:"",emotionalIntelligence:"",interactionFlow:""},technicalLayer:{programmingLanguages:"",frameworks:"",llmModels:"",databases:"",apis:"",infrastructure:""},legalRisk:{privacyConcerns:"",dataProtection:"",compliance:"",risks:"",mitigation:"",ethicalConsiderations:""},currentLanguage:"ar",outputFormat:"markdown",apiSettings:{openaiApiKey:"",openrouterApiKey:""}},n=(0,a.U)()((0,o.tJ)((e,t)=>({...s,updateProjectDefinition:t=>e(e=>({projectDefinition:{...e.projectDefinition,...t}})),updateContextMap:t=>e(e=>({contextMap:{...e.contextMap,...t}})),updateEmotionalTone:t=>e(e=>({emotionalTone:{...e.emotionalTone,...t}})),updateTechnicalLayer:t=>e(e=>({technicalLayer:{...e.technicalLayer,...t}})),updateLegalRisk:t=>e(e=>({legalRisk:{...e.legalRisk,...t}})),setLanguage:t=>e({currentLanguage:t}),setOutputFormat:t=>e({outputFormat:t}),setApiSettings:t=>e({apiSettings:t}),resetAll:()=>e(s),getModuleData:e=>{let r=t();switch(e){case"project":return r.projectDefinition;case"context":return r.contextMap;case"emotional":return r.emotionalTone;case"technical":return r.technicalLayer;case"legal":return r.legalRisk;default:return{}}},getAllData:()=>{let e=t();return{projectDefinition:e.projectDefinition,contextMap:e.contextMap,emotionalTone:e.emotionalTone,technicalLayer:e.technicalLayer,legalRisk:e.legalRisk}}}),{name:"contextkit-storage",version:1}))},6157:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>c});var a=r(9510);r(5023);var o=r(8570);let s=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`),{__esModule:n,$$typeof:i}=s;s.default;let l=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);let c={title:"ContextKit - AI Context Builder",description:"Create organized, actionable context for AI-driven projects"};function d({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),a.jsx("body",{className:"antialiased font-arabic",children:a.jsx(l,{children:e})})]})}},5023:()=>{}};