(()=>{var e={};e.id=526,e.ids=[526],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9484:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>p,routeModule:()=>g,tree:()=>c}),r(9482),r(6157),r(5866);var i=r(3191),a=r(8716),s=r(7922),n=r.n(s),o=r(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["legal-risk",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9482)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6157)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],p=["C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx"],d="/legal-risk/page",u={require:r,loadChunk:()=>Promise.resolve()},g=new i.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/legal-risk/page",pathname:"/legal-risk",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4484:(e,t,r)=>{Promise.resolve().then(r.bind(r,1697))},1697:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var i=r(326),a=r(2561),s=r(8804),n=r(9740),o=r(9190);function l(){let{legalRisk:e,updateLegalRisk:t}=(0,o.l)(),r=(e,r)=>{t({[e]:r})};return i.jsx(a.Z,{title:"Legal & Privacy",titleAr:"القانونية والخصوصية",subtitle:"Address legal, privacy, and ethical considerations for your AI project",subtitleAr:"تناول الاعتبارات القانونية والخصوصية والأخلاقية لمشروع الذكاء الاصطناعي",emoji:"\uD83D\uDD12",moduleKey:"legal-risk",backLink:{href:"/technical-layer",label:"← Back to Technical Layer",labelAr:"← العودة للطبقة التقنية"},nextLink:{href:"/final-preview",label:"Next: Final Preview →",labelAr:"التالي: المعاينة النهائية ←"},rightPanel:i.jsx(n.Z,{moduleData:e,moduleName:"Legal & Privacy",moduleNameAr:"القانونية والخصوصية"}),children:i.jsx("div",{className:"space-y-6",children:[{id:"privacyConcerns",question:"What privacy concerns and data protection requirements apply to your project?",questionAr:"ما هي مخاوف الخصوصية ومتطلبات حماية البيانات التي تنطبق على مشروعك؟",placeholder:"e.g., GDPR compliance, user consent, data anonymization, right to deletion...",placeholderAr:"مثال: الامتثال لـ GDPR، موافقة المستخدم، إخفاء هوية البيانات، الحق في الحذف...",aiSuggestion:"Consider applicable regulations like GDPR, CCPA, and industry-specific requirements.",aiSuggestionAr:"فكر في اللوائح المعمول بها مثل GDPR وCCPA والمتطلبات الخاصة بالصناعة.",promptTemplate:'Help me create a privacy compliance strategy for: "{answer}". Include implementation steps.'},{id:"dataProtection",question:"How will you protect and secure user data?",questionAr:"كيف ستحمي وتؤمن بيانات المستخدمين؟",placeholder:"e.g., Encryption, access controls, secure storage, data minimization...",placeholderAr:"مثال: التشفير، ضوابط الوصول، التخزين الآمن، تقليل البيانات...",aiSuggestion:"Implement defense in depth with encryption, access controls, and monitoring.",aiSuggestionAr:"طبق الدفاع المتعمق مع التشفير وضوابط الوصول والمراقبة.",promptTemplate:'Design a data protection framework for: "{answer}". Include technical and procedural safeguards.'},{id:"compliance",question:"What regulatory compliance requirements must you meet?",questionAr:"ما هي متطلبات الامتثال التنظيمي التي يجب عليك الوفاء بها؟",placeholder:"e.g., HIPAA, SOX, PCI DSS, industry standards, local regulations...",placeholderAr:"مثال: HIPAA، SOX، PCI DSS، معايير الصناعة، اللوائح المحلية...",aiSuggestion:"Research regulations specific to your industry and geographic regions.",aiSuggestionAr:"ابحث في اللوائح الخاصة بصناعتك والمناطق الجغرافية.",promptTemplate:'Create a compliance roadmap for: "{answer}". Include audit and documentation requirements.'},{id:"risks",question:"What are the main risks and potential liabilities?",questionAr:"ما هي المخاطر الرئيسية والمسؤوليات المحتملة؟",placeholder:"e.g., AI bias, data breaches, algorithmic decisions, intellectual property...",placeholderAr:"مثال: تحيز الذكاء الاصطناعي، انتهاكات البيانات، القرارات الخوارزمية، الملكية الفكرية...",aiSuggestion:"Consider technical, legal, ethical, and business risks specific to AI systems.",aiSuggestionAr:"فكر في المخاطر التقنية والقانونية والأخلاقية والتجارية الخاصة بأنظمة الذكاء الاصطناعي.",promptTemplate:'Conduct a risk assessment for: "{answer}". Prioritize risks and suggest mitigation strategies.'},{id:"mitigation",question:"How will you mitigate identified risks?",questionAr:"كيف ستخفف من المخاطر المحددة؟",placeholder:"e.g., Insurance, legal review, testing protocols, monitoring systems...",placeholderAr:"مثال: التأمين، المراجعة القانونية، بروتوكولات الاختبار، أنظمة المراقبة...",aiSuggestion:"Develop both preventive measures and incident response procedures.",aiSuggestionAr:"طور كلاً من التدابير الوقائية وإجراءات الاستجابة للحوادث.",promptTemplate:'Design a risk mitigation plan for: "{answer}". Include monitoring and response procedures.'},{id:"ethicalConsiderations",question:"What ethical considerations and AI governance principles will you follow?",questionAr:"ما هي الاعتبارات الأخلاقية ومبادئ حوكمة الذكاء الاصطناعي التي ستتبعها؟",placeholder:"e.g., Fairness, transparency, accountability, human oversight, explainability...",placeholderAr:"مثال: العدالة، الشفافية، المساءلة، الإشراف البشري، القابلية للتفسير...",aiSuggestion:"Establish clear ethical guidelines and governance frameworks for AI decision-making.",aiSuggestionAr:"ضع مبادئ توجيهية أخلاقية واضحة وأطر حوكمة لاتخاذ قرارات الذكاء الاصطناعي.",promptTemplate:'Create an AI ethics framework for: "{answer}". Include implementation and monitoring guidelines.'}].map(t=>i.jsx(s.Z,{id:t.id,question:t.question,questionAr:t.questionAr,placeholder:t.placeholder,placeholderAr:t.placeholderAr,value:e[t.id]||"",onChange:e=>r(t.id,e),aiSuggestion:t.aiSuggestion,aiSuggestionAr:t.aiSuggestionAr,promptTemplate:t.promptTemplate},t.id))})})}},9482:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>s,default:()=>o});var i=r(8570);let a=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\legal-risk\page.tsx`),{__esModule:s,$$typeof:n}=a;a.default;let o=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\legal-risk\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[948,471,627,224,714],()=>r(9484));module.exports=i})();