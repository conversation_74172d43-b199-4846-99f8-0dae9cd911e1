"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[71],{6071:function(e,t,a){a.d(t,{default:function(){return f}});var r=a(7437),o=a(7138),n=a(8546);function s(){let{theme:e,toggleTheme:t}=(0,n.F)();return(0,r.jsxs)("button",{onClick:t,className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":"Switch to ".concat("light"===e?"dark":"light"," mode"),title:"Switch to ".concat("light"===e?"dark":"light"," mode"),children:[(0,r.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[(0,r.jsx)("svg",{className:"w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ".concat("light"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}),(0,r.jsx)("svg",{className:"w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ".concat("dark"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}var l=a(4792);function i(){let{currentLanguage:e,setLanguage:t}=(0,l.l)();return(0,r.jsxs)("button",{onClick:()=>t("ar"===e?"en":"ar"),className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":"ar"===e?"Switch to English":"التبديل إلى العربية",title:"ar"===e?"Switch to English":"التبديل إلى العربية",children:[(0,r.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[(0,r.jsx)("span",{className:"text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ".concat("en"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"),children:"EN"}),(0,r.jsx)("span",{className:"text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ".concat("ar"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"),children:"عر"})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}var c=a(2265),d=a(4258),u=a(4697),g=a(5489),x=a(7019),p=a(5733),h=a(4436),m=a(9061);function b(e){let{isOpen:t,onClose:a}=e,{apiSettings:o,setApiSettings:n,currentLanguage:s}=(0,l.l)(),[i,b]=(0,c.useState)(o),[y,f]=(0,c.useState)(!1),[v,k]=(0,c.useState)(!1),j="ar"===s,w={title:j?"إعدادات API":"API Settings",openaiLabel:j?"مفتاح OpenAI API":"OpenAI API Key",openrouterLabel:j?"مفتاح OpenRouter API":"OpenRouter API Key",openaiPlaceholder:j?"أدخل مفتاح OpenAI API الخاص بك":"Enter your OpenAI API key",openrouterPlaceholder:j?"أدخل مفتاح OpenRouter API الخاص بك":"Enter your OpenRouter API key",save:j?"حفظ":"Save",cancel:j?"إلغاء":"Cancel",description:j?"قم بإعداد مفاتيح API الخاصة بك لتمكين الإجابات التفاعلية المدعومة بالذكاء الاصطناعي":"Configure your API keys to enable AI-powered interactive responses",openaiDesc:j?"للوصول إلى نماذج GPT من OpenAI":"For accessing GPT models from OpenAI",openrouterDesc:j?"للوصول إلى نماذج متعددة من مقدمي خدمات مختلفين":"For accessing multiple models from various providers"};return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md border border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,r.jsx)(d.Z,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white font-arabic",children:w.title})]}),(0,r.jsx)("button",{onClick:a,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,r.jsx)(u.Z,{className:"w-5 h-5 text-gray-500"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 font-arabic",children:w.description}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic",children:[(0,r.jsx)(g.Z,{className:"w-4 h-4"}),w.openaiLabel]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 font-arabic",children:w.openaiDesc}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:v?"text":"password",value:i.openaiApiKey,onChange:e=>b(t=>({...t,openaiApiKey:e.target.value})),placeholder:w.openaiPlaceholder,className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all font-arabic",dir:j?"rtl":"ltr"}),(0,r.jsx)("button",{type:"button",onClick:()=>k(!v),className:"absolute right-3 top-1/2 -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:v?(0,r.jsx)(x.Z,{className:"w-4 h-4"}):(0,r.jsx)(p.Z,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 font-arabic",children:[(0,r.jsx)(h.Z,{className:"w-4 h-4"}),w.openrouterLabel]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 font-arabic",children:w.openrouterDesc}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:y?"text":"password",value:i.openrouterApiKey,onChange:e=>b(t=>({...t,openrouterApiKey:e.target.value})),placeholder:w.openrouterPlaceholder,className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all font-arabic",dir:j?"rtl":"ltr"}),(0,r.jsx)("button",{type:"button",onClick:()=>f(!y),className:"absolute right-3 top-1/2 -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:y?(0,r.jsx)(x.Z,{className:"w-4 h-4"}):(0,r.jsx)(p.Z,{className:"w-4 h-4"})})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("button",{onClick:()=>{b(o),a()},className:"flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors font-arabic",children:w.cancel}),(0,r.jsxs)("button",{onClick:()=>{n(i),a()},className:"flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-arabic",children:[(0,r.jsx)(m.Z,{className:"w-4 h-4"}),w.save]})]})]})}):null}function y(){let[e,t]=(0,c.useState)(!1),{currentLanguage:a}=(0,l.l)(),o="ar"===a?"إعدادات API":"API Settings";return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{onClick:()=>t(!0),className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":o,title:o,children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)(d.Z,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,r.jsx)(b,{isOpen:e,onClose:()=>t(!1)})]})}function f(e){let{title:t,subtitle:a,backLink:n,emoji:l}=e;return(0,r.jsxs)("header",{className:"relative",children:[(0,r.jsx)("div",{className:"fixed top-4 right-4 z-50 flex gap-3",children:(0,r.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2",children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(y,{}),(0,r.jsx)(i,{}),(0,r.jsx)(s,{})]})})}),(0,r.jsxs)("div",{className:"text-center mb-8 pt-4",children:[n&&(0,r.jsx)(o.default,{href:n.href,className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors",children:n.label}),(0,r.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2",children:[l&&(0,r.jsx)("span",{className:"mr-2",children:l}),t]}),a&&(0,r.jsx)("p",{className:"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:a})]})]})}},8546:function(e,t,a){a.d(t,{F:function(){return l},ThemeProvider:function(){return s}});var r=a(7437),o=a(2265);let n=(0,o.createContext)(void 0);function s(e){let{children:t}=e,[a,s]=(0,o.useState)("light"),[l,i]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{let e=localStorage.getItem("theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";s(e||t),i(!0)},[]),(0,o.useEffect)(()=>{if(l){let e=document.documentElement;"dark"===a?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("theme",a)}},[a,l]),l)?(0,r.jsx)(n.Provider,{value:{theme:a,toggleTheme:()=>{s(e=>"light"===e?"dark":"light")}},children:t}):(0,r.jsx)(r.Fragment,{children:t})}function l(){let e=(0,o.useContext)(n);return void 0===e?{theme:"light",toggleTheme:()=>{}}:e}},4792:function(e,t,a){a.d(t,{l:function(){return s}});var r=a(903),o=a(9291);let n={projectDefinition:{name:"",purpose:"",targetUsers:"",goals:"",scope:"",timeline:""},contextMap:{timeContext:"",language:"",location:"",culturalContext:"",behavioralAspects:"",environmentalFactors:""},emotionalTone:{personality:"",communicationStyle:"",userExperience:"",brandVoice:"",emotionalIntelligence:"",interactionFlow:""},technicalLayer:{programmingLanguages:"",frameworks:"",llmModels:"",databases:"",apis:"",infrastructure:""},legalRisk:{privacyConcerns:"",dataProtection:"",compliance:"",risks:"",mitigation:"",ethicalConsiderations:""},currentLanguage:"ar",outputFormat:"markdown",apiSettings:{openaiApiKey:"",openrouterApiKey:""}},s=(0,r.U)()((0,o.tJ)((e,t)=>({...n,updateProjectDefinition:t=>e(e=>({projectDefinition:{...e.projectDefinition,...t}})),updateContextMap:t=>e(e=>({contextMap:{...e.contextMap,...t}})),updateEmotionalTone:t=>e(e=>({emotionalTone:{...e.emotionalTone,...t}})),updateTechnicalLayer:t=>e(e=>({technicalLayer:{...e.technicalLayer,...t}})),updateLegalRisk:t=>e(e=>({legalRisk:{...e.legalRisk,...t}})),setLanguage:t=>e({currentLanguage:t}),setOutputFormat:t=>e({outputFormat:t}),setApiSettings:t=>e({apiSettings:t}),resetAll:()=>e(n),getModuleData:e=>{let a=t();switch(e){case"project":return a.projectDefinition;case"context":return a.contextMap;case"emotional":return a.emotionalTone;case"technical":return a.technicalLayer;case"legal":return a.legalRisk;default:return{}}},getAllData:()=>{let e=t();return{projectDefinition:e.projectDefinition,contextMap:e.contextMap,emotionalTone:e.emotionalTone,technicalLayer:e.technicalLayer,legalRisk:e.legalRisk}}}),{name:"contextkit-storage",version:1}))}}]);