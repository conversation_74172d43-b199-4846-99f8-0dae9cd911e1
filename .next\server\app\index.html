<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/58b23af34de8eeb5.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-1796d5ecd61cbd74.js"/><script src="/_next/static/chunks/fd9d1056-24a270b5b0bed3f7.js" async=""></script><script src="/_next/static/chunks/23-5093ff7c951ff2bb.js" async=""></script><script src="/_next/static/chunks/main-app-2e8d005458837cce.js" async=""></script><script src="/_next/static/chunks/220-9c9660d07eeece7b.js" async=""></script><script src="/_next/static/chunks/71-bb15186e5604dd7f.js" async=""></script><script src="/_next/static/chunks/app/page-98d693b9d82a8b54.js" async=""></script><script src="/_next/static/chunks/app/layout-2bbff9afd2c671be.js" async=""></script><link rel="preconnect" href="https://fonts.googleapis.com"/><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous"/><title>ContextKit - AI Context Builder</title><meta name="description" content="Create organized, actionable context for AI-driven projects"/><script src="/_next/static/chunks/polyfills-78c92fac7aa8fdd8.js" noModule=""></script></head><body class="antialiased font-arabic"><div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 font-arabic" dir="rtl"><div class="container mx-auto px-4 py-16"><header class="relative"><div class="fixed top-4 right-4 z-50 flex gap-3"><div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2"><div class="flex gap-2"><button class="relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden" aria-label="إعدادات API" title="إعدادات API"><div class="absolute inset-0 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90" aria-hidden="true"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg></div><div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div></button><button class="relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden" aria-label="Switch to English" title="Switch to English"><div class="absolute inset-0 flex items-center justify-center"><span class="text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out opacity-0 rotate-180 scale-0">EN</span><span class="text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic opacity-100 rotate-0 scale-100">عر</span></div><div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div></button><button class="relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden" aria-label="Switch to dark mode" title="Switch to dark mode"><div class="absolute inset-0 flex items-center justify-center"><svg class="w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out opacity-100 rotate-0 scale-100" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg><svg class="w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out opacity-0 -rotate-180 scale-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg></div><div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div></button></div></div></div><div class="text-center mb-8 pt-4"><h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2"><span class="mr-2">🧠</span>ContextKit</h1><p class="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">تطبيق ويب تفاعلي متعدد الصفحات مبتكر مصمم لتبسيط إنشاء سياق منظم ومتماسك وقابل للتنفيذ للمشاريع المدفوعة بالذكاء الاصطناعي.</p></div></header><div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"><div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg"><div class="text-3xl mb-4">🧭</div><h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">واجهة متعددة الصفحات</h3><p class="text-gray-600 dark:text-gray-300">صفحات مخصصة لكل وحدة مع أسئلة ومخرجات مصممة خصيصاً.</p></div><div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg"><div class="text-3xl mb-4">✍️</div><h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">أسئلة موجهة</h3><p class="text-gray-600 dark:text-gray-300">أسئلة ذكية لتوجيه عملية تفكيرك عبر كل وحدة.</p></div><div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg"><div class="text-3xl mb-4">📋</div><h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">نسخ المخرجات</h3><p class="text-gray-600 dark:text-gray-300">أزرار سهلة الاستخدام لنسخ المخرجات الكاملة أو الإجابات الفردية.</p></div><div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg"><div class="text-3xl mb-4">🧾</div><h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">مخرجات منظمة</h3><p class="text-gray-600 dark:text-gray-300">مخرجات بصيغة Markdown أو HTML أو JSON للتكامل السهل.</p></div><div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg"><div class="text-3xl mb-4">💾</div><h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">حفظ تلقائي وجلسات</h3><p class="text-gray-600 dark:text-gray-300">حفظ واسترجاع تلقائي لجلسات المستخدم للراحة.</p></div><div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg"><div class="text-3xl mb-4">🌐</div><h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">دعم متعدد اللغات</h3><p class="text-gray-600 dark:text-gray-300">واجهات باللغتين الإنجليزية والعربية لخدمة جمهور أوسع.</p></div></div><section class="mb-16"><h2 class="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">🛠️ <!-- -->الوحدات المتاحة</h2><div class="grid md:grid-cols-2 gap-6"><a href="/project-definition" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow block"><h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">🎯 <!-- -->تعريف المشروع</h3><p class="text-gray-600 dark:text-gray-300">حدد نطاق مشروعك والمستخدمين والأهداف بأسئلة موجهة.</p></a><a href="/context-map" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block"><h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">🗺️ <!-- -->خريطة السياق</h3><p class="text-gray-600 dark:text-gray-300">حدد الوقت واللغة والموقع والجوانب السلوكية لمشروعك.</p></a><a href="/emotional-tone" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-purple-500 hover:shadow-xl transition-shadow block"><h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">✨ <!-- -->النبرة والتجربة</h3><p class="text-gray-600 dark:text-gray-300">احدد النبرة العامة وتجربة المستخدم المرغوبة لمشروعك.</p></a><a href="/technical-layer" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-orange-500 hover:shadow-xl transition-shadow block"><h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">⚙️ <!-- -->الطبقة التقنية</h3><p class="text-gray-600 dark:text-gray-300">حدد المتطلبات التقنية والأدوات والنماذج المستخدمة.</p></a><a href="/legal-risk" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-red-500 hover:shadow-xl transition-shadow block"><h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">🔒 <!-- -->التحديات والخصوصية</h3><p class="text-gray-600 dark:text-gray-300">تناول التحديات ومخاوف الخصوصية والقضايا التنظيمية.</p></a><a href="/final-preview" class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-indigo-500 hover:shadow-xl transition-shadow block"><h3 class="text-xl font-semibold mb-3 text-gray-900 dark:text-white">📋 <!-- -->المعاينة النهائية</h3><p class="text-gray-600 dark:text-gray-300">راجع واستخرج السياق الكامل لمشروعك.</p></a></div></section><div class="text-center"><a href="/project-definition" class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200">ابدأ ببناء السياق</a><p class="mt-4 text-gray-600 dark:text-gray-300">هل أنت مستعد لإنشاء سياق منظم وقابل للتنفيذ لمشاريع الذكاء الاصطناعي؟</p></div></div></div><script src="/_next/static/chunks/webpack-1796d5ecd61cbd74.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/58b23af34de8eeb5.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"2:I[5751,[],\"\"]\n4:I[6513,[],\"ClientPageRoot\"]\n5:I[8212,[\"220\",\"static/chunks/220-9c9660d07eeece7b.js\",\"71\",\"static/chunks/71-bb15186e5604dd7f.js\",\"931\",\"static/chunks/app/page-98d693b9d82a8b54.js\"],\"default\"]\n6:I[8546,[\"185\",\"static/chunks/app/layout-2bbff9afd2c671be.js\"],\"ThemeProvider\"]\n7:I[9275,[],\"\"]\n8:I[1343,[],\"\"]\na:I[6130,[],\"\"]\nb:[]\n"])</script><script>self.__next_f.push([1,"0:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/58b23af34de8eeb5.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"$L2\",null,{\"buildId\":\"xDl8_gFyyuKlPUhKZkwYl\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/\",\"initialTree\":[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"__PAGE__\",{},[[\"$L3\",[\"$\",\"$L4\",null,{\"props\":{\"params\":{},\"searchParams\":{}},\"Component\":\"$5\"}]],null],null]},[[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://fonts.googleapis.com\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://fonts.gstatic.com\",\"crossOrigin\":\"anonymous\"}]]}],[\"$\",\"body\",null,{\"className\":\"antialiased font-arabic\",\"children\":[\"$\",\"$L6\",null,{\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}]]}],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$L9\"],\"globalErrorComponent\":\"$a\",\"missingSlots\":\"$Wb\"}]]\n"])</script><script>self.__next_f.push([1,"9:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"ContextKit - AI Context Builder\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Create organized, actionable context for AI-driven projects\"}]]\n3:null\n"])</script></body></html>