<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/58b23af34de8eeb5.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-1796d5ecd61cbd74.js"/><script src="/_next/static/chunks/fd9d1056-24a270b5b0bed3f7.js" async=""></script><script src="/_next/static/chunks/23-5093ff7c951ff2bb.js" async=""></script><script src="/_next/static/chunks/main-app-2e8d005458837cce.js" async=""></script><script src="/_next/static/chunks/220-9c9660d07eeece7b.js" async=""></script><script src="/_next/static/chunks/71-bb15186e5604dd7f.js" async=""></script><script src="/_next/static/chunks/app/vibe/page-1a8a33af938ee581.js" async=""></script><script src="/_next/static/chunks/app/layout-2bbff9afd2c671be.js" async=""></script><link rel="preconnect" href="https://fonts.googleapis.com"/><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous"/><title>ContextKit - AI Context Builder</title><meta name="description" content="Create organized, actionable context for AI-driven projects"/><script src="/_next/static/chunks/polyfills-78c92fac7aa8fdd8.js" noModule=""></script></head><body class="antialiased font-arabic"><div class="min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800"><div class="container mx-auto px-4 py-16"><header class="relative"><div class="fixed top-4 right-4 z-50 flex gap-3"><div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2"><div class="flex gap-2"><button class="relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden" aria-label="إعدادات API" title="إعدادات API"><div class="absolute inset-0 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-settings w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90" aria-hidden="true"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg></div><div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div></button><button class="relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden" aria-label="Switch to English" title="Switch to English"><div class="absolute inset-0 flex items-center justify-center"><span class="text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out opacity-0 rotate-180 scale-0">EN</span><span class="text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic opacity-100 rotate-0 scale-100">عر</span></div><div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div></button><button class="relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden" aria-label="Switch to dark mode" title="Switch to dark mode"><div class="absolute inset-0 flex items-center justify-center"><svg class="w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out opacity-100 rotate-0 scale-100" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg><svg class="w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out opacity-0 -rotate-180 scale-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg></div><div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div></button></div></div></div><div class="text-center mb-8 pt-4"><a class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors" href="/context-map">← Back to Context Map</a><h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2"><span class="mr-2">✨</span>Vibe &amp; Experience</h1><p class="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">Define the tone, personality, and user experience for your AI project</p></div></header><div class="max-w-2xl mx-auto"><div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12 text-center"><div class="text-6xl mb-6">🚧</div><h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Coming Soon</h2><p class="text-lg text-gray-600 dark:text-gray-300 mb-8">The Vibe &amp; Experience module is currently under development. This module will help you define the personality, tone, and user experience aspects of your AI project.</p><div class="space-y-4 text-left bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-8"><h3 class="font-semibold text-gray-900 dark:text-white">This module will include:</h3><ul class="space-y-2 text-gray-600 dark:text-gray-300"><li>• Personality and tone definition</li><li>• User experience preferences</li><li>• Communication style guidelines</li><li>• Brand voice and messaging</li><li>• Interaction flow design</li><li>• Emotional intelligence requirements</li></ul></div><div class="flex justify-center space-x-4"><a class="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors" href="/context-map">← Previous Module</a><a class="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors" href="/">Back to Home</a></div></div></div></div></div><script src="/_next/static/chunks/webpack-1796d5ecd61cbd74.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/58b23af34de8eeb5.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"2:I[5751,[],\"\"]\n4:I[6071,[\"220\",\"static/chunks/220-9c9660d07eeece7b.js\",\"71\",\"static/chunks/71-bb15186e5604dd7f.js\",\"94\",\"static/chunks/app/vibe/page-1a8a33af938ee581.js\"],\"default\"]\n5:I[231,[\"220\",\"static/chunks/220-9c9660d07eeece7b.js\",\"71\",\"static/chunks/71-bb15186e5604dd7f.js\",\"94\",\"static/chunks/app/vibe/page-1a8a33af938ee581.js\"],\"\"]\n6:I[9275,[],\"\"]\n7:I[1343,[],\"\"]\n8:I[8546,[\"185\",\"static/chunks/app/layout-2bbff9afd2c671be.js\"],\"ThemeProvider\"]\na:I[6130,[],\"\"]\nb:[]\n"])</script><script>self.__next_f.push([1,"0:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/58b23af34de8eeb5.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"$L2\",null,{\"buildId\":\"xDl8_gFyyuKlPUhKZkwYl\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/vibe\",\"initialTree\":[\"\",{\"children\":[\"vibe\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"vibe\",{\"children\":[\"__PAGE__\",{},[[\"$L3\",[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800\",\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4 py-16\",\"children\":[[\"$\",\"$L4\",null,{\"title\":\"Vibe \u0026 Experience\",\"subtitle\":\"Define the tone, personality, and user experience for your AI project\",\"emoji\":\"✨\",\"backLink\":{\"href\":\"/context-map\",\"label\":\"← Back to Context Map\"}}],[\"$\",\"div\",null,{\"className\":\"max-w-2xl mx-auto\",\"children\":[\"$\",\"div\",null,{\"className\":\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12 text-center\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-6xl mb-6\",\"children\":\"🚧\"}],[\"$\",\"h2\",null,{\"className\":\"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\"children\":\"Coming Soon\"}],[\"$\",\"p\",null,{\"className\":\"text-lg text-gray-600 dark:text-gray-300 mb-8\",\"children\":\"The Vibe \u0026 Experience module is currently under development. This module will help you define the personality, tone, and user experience aspects of your AI project.\"}],[\"$\",\"div\",null,{\"className\":\"space-y-4 text-left bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-8\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"font-semibold text-gray-900 dark:text-white\",\"children\":\"This module will include:\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2 text-gray-600 dark:text-gray-300\",\"children\":[[\"$\",\"li\",null,{\"children\":\"• Personality and tone definition\"}],[\"$\",\"li\",null,{\"children\":\"• User experience preferences\"}],[\"$\",\"li\",null,{\"children\":\"• Communication style guidelines\"}],[\"$\",\"li\",null,{\"children\":\"• Brand voice and messaging\"}],[\"$\",\"li\",null,{\"children\":\"• Interaction flow design\"}],[\"$\",\"li\",null,{\"children\":\"• Emotional intelligence requirements\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"flex justify-center space-x-4\",\"children\":[[\"$\",\"$L5\",null,{\"href\":\"/context-map\",\"className\":\"px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors\",\"children\":\"← Previous Module\"}],[\"$\",\"$L5\",null,{\"href\":\"/\",\"className\":\"px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors\",\"children\":\"Back to Home\"}]]}]]}]}]]}]}]],null],null]},[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"vibe\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\",\"styles\":null}],null]},[[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://fonts.googleapis.com\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://fonts.gstatic.com\",\"crossOrigin\":\"anonymous\"}]]}],[\"$\",\"body\",null,{\"className\":\"antialiased font-arabic\",\"children\":[\"$\",\"$L8\",null,{\"children\":[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"styles\":null}]}]}]]}],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$L9\"],\"globalErrorComponent\":\"$a\",\"missingSlots\":\"$Wb\"}]]\n"])</script><script>self.__next_f.push([1,"9:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"ContextKit - AI Context Builder\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Create organized, actionable context for AI-driven projects\"}]]\n3:null\n"])</script></body></html>