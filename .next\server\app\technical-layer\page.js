(()=>{var e={};e.id=391,e.ids=[391],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1648:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>p,routeModule:()=>g,tree:()=>c}),a(6180),a(6157),a(5866);var r=a(3191),i=a(8716),s=a(7922),n=a.n(s),o=a(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c=["",{children:["technical-layer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,6180)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,6157)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,5866,23)),"next/dist/client/components/not-found-error"]}],p=["C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx"],u="/technical-layer/page",d={require:a,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/technical-layer/page",pathname:"/technical-layer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7546:(e,t,a)=>{Promise.resolve().then(a.bind(a,2980))},2980:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(326),i=a(2561),s=a(8804),n=a(9740),o=a(9190);function l(){let{technicalLayer:e,updateTechnicalLayer:t}=(0,o.l)(),a=(e,a)=>{t({[e]:a})};return r.jsx(i.Z,{title:"Technical Layer",titleAr:"الطبقة التقنية",subtitle:"Define the technical architecture and tools for your AI project",subtitleAr:"حدد الهندسة التقنية والأدوات لمشروع الذكاء الاصطناعي",emoji:"⚙️",moduleKey:"technical-layer",backLink:{href:"/emotional-tone",label:"← Back to Emotional Tone",labelAr:"← العودة للنبرة العاطفية"},nextLink:{href:"/legal-risk",label:"Next: Legal & Privacy →",labelAr:"التالي: القانونية والخصوصية ←"},rightPanel:r.jsx(n.Z,{moduleData:e,moduleName:"Technical Layer",moduleNameAr:"الطبقة التقنية"}),children:r.jsx("div",{className:"space-y-6",children:[{id:"programmingLanguages",question:"What programming languages will be used in your project?",questionAr:"ما هي لغات البرمجة التي ستُستخدم في مشروعك؟",placeholder:"e.g., Python, JavaScript, TypeScript, Java, C#...",placeholderAr:"مثال: Python، JavaScript، TypeScript، Java، C#...",type:"text",aiSuggestion:"Consider the AI/ML libraries available, team expertise, and performance requirements.",aiSuggestionAr:"فكر في مكتبات الذكاء الاصطناعي المتاحة وخبرة الفريق ومتطلبات الأداء.",promptTemplate:'Help me choose the best programming languages for this AI project: "{answer}". Consider scalability and ecosystem.'},{id:"frameworks",question:"What frameworks and libraries will you use?",questionAr:"ما هي الأطر والمكتبات التي ستستخدمها؟",placeholder:"e.g., TensorFlow, PyTorch, React, Next.js, FastAPI, Django...",placeholderAr:"مثال: TensorFlow، PyTorch، React، Next.js، FastAPI، Django...",aiSuggestion:"Choose frameworks that align with your project goals and team capabilities.",aiSuggestionAr:"اختر الأطر التي تتماشى مع أهداف مشروعك وقدرات فريقك.",promptTemplate:'Analyze this technology stack: "{answer}". Suggest optimizations and alternatives.'},{id:"llmModels",question:"Which LLM models and AI services will you integrate?",questionAr:"ما هي نماذج اللغة الكبيرة وخدمات الذكاء الاصطناعي التي ستدمجها؟",placeholder:"e.g., GPT-4, Claude, Gemini, Local models, OpenAI API, Anthropic API...",placeholderAr:"مثال: GPT-4، Claude، Gemini، نماذج محلية، OpenAI API، Anthropic API...",aiSuggestion:"Consider cost, performance, privacy requirements, and specific capabilities needed.",aiSuggestionAr:"فكر في التكلفة والأداء ومتطلبات الخصوصية والقدرات المحددة المطلوبة.",promptTemplate:'Help me design an LLM integration strategy for: "{answer}". Include fallback options.'},{id:"databases",question:"What databases and data storage solutions will you use?",questionAr:"ما هي قواعد البيانات وحلول تخزين البيانات التي ستستخدمها؟",placeholder:"e.g., PostgreSQL, MongoDB, Redis, Vector databases, Cloud storage...",placeholderAr:"مثال: PostgreSQL، MongoDB، Redis، قواعد بيانات المتجهات، التخزين السحابي...",aiSuggestion:"Consider data types, scalability, consistency requirements, and AI-specific needs.",aiSuggestionAr:"فكر في أنواع البيانات وقابلية التوسع ومتطلبات الاتساق والاحتياجات الخاصة بالذكاء الاصطناعي.",promptTemplate:'Design a data architecture for: "{answer}". Include backup and scaling strategies.'},{id:"apis",question:"What APIs and external services will you integrate?",questionAr:"ما هي واجهات برمجة التطبيقات والخدمات الخارجية التي ستدمجها؟",placeholder:"e.g., REST APIs, GraphQL, Third-party services, Payment gateways...",placeholderAr:"مثال: REST APIs، GraphQL، خدمات طرف ثالث، بوابات الدفع...",aiSuggestion:"Plan for API rate limits, authentication, error handling, and monitoring.",aiSuggestionAr:"خطط لحدود معدل API والمصادقة ومعالجة الأخطاء والمراقبة.",promptTemplate:'Create an API integration plan for: "{answer}". Include security and reliability measures.'},{id:"infrastructure",question:"What infrastructure and deployment strategy will you use?",questionAr:"ما هي البنية التحتية واستراتيجية النشر التي ستستخدمها؟",placeholder:"e.g., AWS, Google Cloud, Azure, Docker, Kubernetes, Serverless...",placeholderAr:"مثال: AWS، Google Cloud، Azure، Docker، Kubernetes، Serverless...",aiSuggestion:"Consider scalability, cost optimization, security, and maintenance requirements.",aiSuggestionAr:"فكر في قابلية التوسع وتحسين التكلفة والأمان ومتطلبات الصيانة.",promptTemplate:'Design an infrastructure architecture for: "{answer}". Include monitoring and scaling plans.'}].map(t=>r.jsx(s.Z,{id:t.id,question:t.question,questionAr:t.questionAr,placeholder:t.placeholder,placeholderAr:t.placeholderAr,value:e[t.id]||"",onChange:e=>a(t.id,e),type:t.type,aiSuggestion:t.aiSuggestion,aiSuggestionAr:t.aiSuggestionAr,promptTemplate:t.promptTemplate},t.id))})})}},6180:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>n,__esModule:()=>s,default:()=>o});var r=a(8570);let i=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\technical-layer\page.tsx`),{__esModule:s,$$typeof:n}=i;i.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\technical-layer\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[948,471,627,224,714],()=>a(1648));module.exports=r})();