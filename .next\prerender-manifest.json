{"version": 4, "routes": {"/emotional-tone": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/emotional-tone", "dataRoute": "/emotional-tone.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/context-map": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/context-map", "dataRoute": "/context-map.rsc"}, "/technical-layer": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/technical-layer", "dataRoute": "/technical-layer.rsc"}, "/vibe": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/vibe", "dataRoute": "/vibe.rsc"}, "/project-definition": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/project-definition", "dataRoute": "/project-definition.rsc"}, "/legal-risk": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/legal-risk", "dataRoute": "/legal-risk.rsc"}, "/final-preview": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/final-preview", "dataRoute": "/final-preview.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "57f235cf91d817522620fbb111ba4b17", "previewModeSigningKey": "2e93f6f93f8b41868cad9a458d67de5395261503942a7a66c1e73b804388f951", "previewModeEncryptionKey": "3b6f3cf3d7c78a3c94b78b56ff41c498951c3f3ef2c615e835450a42fd596f86"}}