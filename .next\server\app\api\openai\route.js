"use strict";(()=>{var e={};e.id=773,e.ids=[773],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3967:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>h,requestAsyncStorage:()=>c,routeModule:()=>u,serverHooks:()=>l,staticGenerationAsyncStorage:()=>d});var o={};r.r(o),r.d(o,{POST:()=>p});var s=r(9303),a=r(8716),n=r(670),i=r(7070);async function p(e){try{let{apiKey:t,messages:r}=await e.json();if(!t)return i.NextResponse.json({error:"API key is required"},{status:400});let o=await fetch("https://api.openai.com/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({model:"gpt-3.5-turbo",messages:r,max_tokens:1e3,temperature:.7})});if(!o.ok){let e=await o.text();return i.NextResponse.json({error:"OpenAI API error",details:e},{status:o.status})}let s=await o.json(),a=s.choices?.[0]?.message?.content||"";return i.NextResponse.json({content:a})}catch(e){return console.error("OpenAI API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/openai/route",pathname:"/api/openai",filename:"route",bundlePath:"app/api/openai/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openai\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:c,staticGenerationAsyncStorage:d,serverHooks:l}=u,m="/api/openai/route";function h(){return(0,n.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:d})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[948,972],()=>r(3967));module.exports=o})();