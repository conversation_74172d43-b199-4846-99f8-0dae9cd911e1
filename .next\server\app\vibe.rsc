2:I[6071,["220","static/chunks/220-9c9660d07eeece7b.js","71","static/chunks/71-bb15186e5604dd7f.js","94","static/chunks/app/vibe/page-1a8a33af938ee581.js"],"default"]
3:I[231,["220","static/chunks/220-9c9660d07eeece7b.js","71","static/chunks/71-bb15186e5604dd7f.js","94","static/chunks/app/vibe/page-1a8a33af938ee581.js"],""]
4:I[9275,[],""]
5:I[1343,[],""]
6:I[8546,["185","static/chunks/app/layout-2bbff9afd2c671be.js"],"ThemeProvider"]
0:["xDl8_gFyyuKlPUhKZkwYl",[[["",{"children":["vibe",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",{"children":["vibe",{"children":["__PAGE__",{},[["$L1",["$","div",null,{"className":"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800","children":["$","div",null,{"className":"container mx-auto px-4 py-16","children":[["$","$L2",null,{"title":"Vibe & Experience","subtitle":"Define the tone, personality, and user experience for your AI project","emoji":"✨","backLink":{"href":"/context-map","label":"← Back to Context Map"}}],["$","div",null,{"className":"max-w-2xl mx-auto","children":["$","div",null,{"className":"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12 text-center","children":[["$","div",null,{"className":"text-6xl mb-6","children":"🚧"}],["$","h2",null,{"className":"text-3xl font-bold text-gray-900 dark:text-white mb-4","children":"Coming Soon"}],["$","p",null,{"className":"text-lg text-gray-600 dark:text-gray-300 mb-8","children":"The Vibe & Experience module is currently under development. This module will help you define the personality, tone, and user experience aspects of your AI project."}],["$","div",null,{"className":"space-y-4 text-left bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-8","children":[["$","h3",null,{"className":"font-semibold text-gray-900 dark:text-white","children":"This module will include:"}],["$","ul",null,{"className":"space-y-2 text-gray-600 dark:text-gray-300","children":[["$","li",null,{"children":"• Personality and tone definition"}],["$","li",null,{"children":"• User experience preferences"}],["$","li",null,{"children":"• Communication style guidelines"}],["$","li",null,{"children":"• Brand voice and messaging"}],["$","li",null,{"children":"• Interaction flow design"}],["$","li",null,{"children":"• Emotional intelligence requirements"}]]}]]}],["$","div",null,{"className":"flex justify-center space-x-4","children":[["$","$L3",null,{"href":"/context-map","className":"px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors","children":"← Previous Module"}],["$","$L3",null,{"href":"/","className":"px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors","children":"Back to Home"}]]}]]}]}]]}]}]],null],null]},["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children","vibe","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}],null]},[["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":"anonymous"}]]}],["$","body",null,{"className":"antialiased font-arabic","children":["$","$L6",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"styles":null}]}]}]]}],null],null],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/58b23af34de8eeb5.css","precedence":"next","crossOrigin":"$undefined"}]],[null,"$L7"]]]]]
7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"ContextKit - AI Context Builder"}],["$","meta","3",{"name":"description","content":"Create organized, actionable context for AI-driven projects"}]]
1:null
