(()=>{var e={};e.id=795,e.ids=[795],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9e3:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,originalPathname:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>p}),r(922),r(6157),r(5866);var o=r(3191),s=r(8716),i=r(7922),a=r.n(i),n=r(5231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let p=["",{children:["context-map",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,922)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6157)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx"],c="/context-map/page",d={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/context-map/page",pathname:"/context-map",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},88:(e,t,r)=>{Promise.resolve().then(r.bind(r,6886))},6886:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var o=r(326),s=r(2561),i=r(8804),a=r(9740),n=r(9190);function l(){let{contextMap:e,updateContextMap:t}=(0,n.l)(),r=(e,r)=>{t({[e]:r})};return o.jsx(s.Z,{title:"Context Map",titleAr:"خريطة السياق",subtitle:"Define the contextual framework for your AI project",subtitleAr:"حدد الإطار السياقي لمشروع الذكاء الاصطناعي",emoji:"\uD83D\uDDFA️",moduleKey:"context-map",backLink:{href:"/project-definition",label:"← Back to Project Definition",labelAr:"← العودة لتعريف المشروع"},nextLink:{href:"/emotional-tone",label:"Next: Emotional Tone →",labelAr:"التالي: النبرة العاطفية ←"},rightPanel:o.jsx(a.Z,{moduleData:e,moduleName:"Context Map",moduleNameAr:"خريطة السياق"}),children:o.jsx("div",{className:"space-y-6",children:[{id:"timeContext",question:"What is the temporal context of your project?",questionAr:"ما هو السياق الزمني لمشروعك؟",placeholder:"e.g., Global 24/7 support, Business hours EST, Real-time responses...",placeholderAr:"مثال: دعم عالمي على مدار الساعة، ساعات العمل بتوقيت شرق أمريكا، استجابات فورية...",aiSuggestion:"Consider time zones, working hours, response time expectations, and any time-sensitive requirements.",aiSuggestionAr:"فكر في المناطق الزمنية وساعات العمل وتوقعات وقت الاستجابة وأي متطلبات حساسة للوقت.",promptTemplate:'Help me optimize this temporal context for an AI project: "{answer}". Suggest improvements for better time management.'},{id:"language",question:"What languages should your AI system support?",questionAr:"ما هي اللغات التي يجب أن يدعمها نظام الذكاء الاصطناعي؟",placeholder:"e.g., English primary, Arabic secondary, Multilingual support...",placeholderAr:"مثال: الإنجليزية أساسية، العربية ثانوية، دعم متعدد اللغات...",type:"text",aiSuggestion:"Consider your target audience, regional requirements, and the complexity of multilingual support.",aiSuggestionAr:"فكر في جمهورك المستهدف والمتطلبات الإقليمية وتعقيد الدعم متعدد اللغات.",promptTemplate:'Analyze this language requirement for an AI system: "{answer}". Suggest implementation strategies.'},{id:"location",question:"What geographic regions or locations will this project serve?",questionAr:"ما هي المناطق الجغرافية أو المواقع التي سيخدمها هذا المشروع؟",placeholder:"e.g., Middle East, North America, Global, Specific cities...",placeholderAr:"مثال: الشرق الأوسط، أمريكا الشمالية، عالمي، مدن محددة...",aiSuggestion:"Think about regional regulations, cultural differences, and infrastructure requirements.",aiSuggestionAr:"فكر في اللوائح الإقليمية والاختلافات الثقافية ومتطلبات البنية التحتية.",promptTemplate:'Help me understand the geographic implications of this scope: "{answer}". What should I consider?'}].map(t=>o.jsx(i.Z,{id:t.id,question:t.question,questionAr:t.questionAr,placeholder:t.placeholder,placeholderAr:t.placeholderAr,value:e[t.id]||"",onChange:e=>r(t.id,e),type:t.type,aiSuggestion:t.aiSuggestion,aiSuggestionAr:t.aiSuggestionAr,promptTemplate:t.promptTemplate},t.id))})})}},922:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>n});var o=r(8570);let s=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\context-map\page.tsx`),{__esModule:i,$$typeof:a}=s;s.default;let n=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\context-map\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[948,471,627,224,714],()=>r(9e3));module.exports=o})();