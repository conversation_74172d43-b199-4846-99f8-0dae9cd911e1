{"/_not-found/page": "/_not-found", "/api/openrouter/route": "/api/openrouter", "/context-map/page": "/context-map", "/emotional-tone/page": "/emotional-tone", "/api/openai/route": "/api/openai", "/final-preview/page": "/final-preview", "/page": "/", "/project-definition/page": "/project-definition", "/legal-risk/page": "/legal-risk", "/technical-layer/page": "/technical-layer", "/vibe/page": "/vibe"}