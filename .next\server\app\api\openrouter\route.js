"use strict";(()=>{var e={};e.id=547,e.ids=[547],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>m,requestAsyncStorage:()=>c,routeModule:()=>u,serverHooks:()=>l,staticGenerationAsyncStorage:()=>d});var o={};r.r(o),r.d(o,{POST:()=>p});var s=r(9303),n=r(8716),a=r(670),i=r(7070);async function p(e){try{let{apiKey:t,messages:r}=await e.json();if(!t)return i.NextResponse.json({error:"API key is required"},{status:400});let o=await fetch("https://openrouter.ai/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`,"HTTP-Referer":process.env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000","X-Title":"ContextKit"},body:JSON.stringify({model:"openai/gpt-3.5-turbo",messages:r,max_tokens:1e3,temperature:.7})});if(!o.ok){let e=await o.text();return i.NextResponse.json({error:"OpenRouter API error",details:e},{status:o.status})}let s=await o.json(),n=s.choices?.[0]?.message?.content||"";return i.NextResponse.json({content:n})}catch(e){return console.error("OpenRouter API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/openrouter/route",pathname:"/api/openrouter",filename:"route",bundlePath:"app/api/openrouter/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openrouter\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:c,staticGenerationAsyncStorage:d,serverHooks:l}=u,h="/api/openrouter/route";function m(){return(0,a.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:d})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[948,972],()=>r(6487));module.exports=o})();